const { uploadImg } = require("./google.config");
const {
  createMsg,
  createSender,
  createAttachmentMsg,
  createPostBackMsg,
  mapSettings,
  createPostBackID,
} = require("./scripts/Helper/requestMapping");
var filter = require("leo-profanity");
const app = require("./app.config");
const server = require("http").createServer(app);

// Configure Express to trust proxy headers for Heroku
app.set("trust proxy", 1);
const { replyBack, checkIfbanned } = require("./scripts/requestHandler");
const io = require("socket.io")(server, {
  // Transport configuration
  transports: ["websocket", "polling"],

  // Timeout configuration for production environments
  pingTimeout: 60000, // 60 seconds - increased from default 5000ms for Heroku
  pingInterval: 25000, // 25 seconds - increased from default 25000ms

  // CORS configuration for production
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  },

  // Allow upgrades (websocket fallback to polling)
  allowUpgrades: true,

  // HTTP compression
  perMessageDeflate: {
    threshold: 1024, // Bytes
    zlibDeflateOptions: {
      chunkSize: 16 * 1024,
    },
  },

  // Max HTTP buffer size
  maxHttpBufferSize: 1e6, // 1MB

  // Connection state recovery
  connectionStateRecovery: {
    maxDisconnectionDuration: 2 * 60 * 1000, // 2 minutes
    skipMiddlewares: true,
  },

  // Heroku-specific configuration
  // Enable proxy trust for Heroku's load balancer
  cookie: {
    httpOnly: false,
    sameSite: "lax",
  }
});

let images = [];
let maxConnectedUsers = 0;
// eslint-disable-next-line max-statements
io.on("connection", async (socket) => {
  const sockets = await io.fetchSockets();
  maxConnectedUsers = Math.max(maxConnectedUsers, sockets.length);
  if (!socket.handshake.query?.deviceId) {
    sendMessageToUser(socket.id, {
      text: "Please update your application, the application won't work",
    });
  } else {
    let sender = createSender(
      socket.id,
      socket.handshake.query?.deviceId,
      socket.handshake.query?.channelId
    );
    //check if banned
    let isBanned = await checkIfbanned(sender);
    if (isBanned) {
      return;
    }
    socket.on("chat message", chatMessage(sender, socket));
    socket.on("chat", chat(sender, socket));
    socket.on("typing", typing(socket));
    socket.on("saveSettings", saveSettings(sender));
    socket.on("image", image(socket));
    socket.on("imageSent", imageSent(socket, sender));
    socket.on("disconnect", postbackMsg(socket, sender, "DISCONNECT"));
    socket.on("logout", postbackMsg(socket, sender, "DISCONNECT"));
    socket.on("block", block(socket, sender, "BLOCK"));
    socket.on("report", block(socket, sender, "REPORT"));
    socket.on("reconnect", postbackMsg(socket, sender, "RECONNECT"));
    socket.on("error", postbackMsg(socket, sender, "RECONNECT"));
    io.to(sender.sessionId).emit("tabsLinks", {
      games: process.env.gamesURI,
      quiz: process.env.quizURI,
    });
  }
});
function block(socket, sender, type) {
  return (msg) => {
    delete images[socket.id];
    if (msg) replyBack(createPostBackID(sender, type, msg.id));
    else replyBack(createPostBackMsg(sender, type));
  };
}

function postbackMsg(socket, sender, type) {
  return () => {
    delete images[socket.id];
    replyBack(createPostBackMsg(sender, type));
  };
}

function imageSent(socket, sender) {
  return (msg, ack) => {
    if (images && images[socket.id] && images[socket.id][msg.id]) {
      let base64 = images[socket.id][msg.id];
      let resp = uploadImg(
        base64,
        socket.handshake.query?.deviceId,
        msg.id + "." + (msg.ext || "png")
      );
      delete images[socket.id][msg.id];
      if (images[socket.id].length === 0) delete images[socket.id];
      resp
        .then((imgUrl) => {
          console.log(imgUrl);
          replyBack(createAttachmentMsg(sender, imgUrl));
          io.to(socket.id).emit("ack", { id: msg.id });
        })
        .catch(console.error);
      ack("image recieved");
    }
  };
}

function image(socket) {
  return (msg, ack) => {
    //process data with  user
    if (!images) images = [];
    if (!images[socket.id]) images[socket.id] = [];
    if (!images[socket.id][msg.id]) images[socket.id][msg.id] = msg.data;
    else images[socket.id][msg.id] += msg.data;
    ack(msg.index);
  };
}

function saveSettings(sender) {
  return (settings) => {
    replyBack(mapSettings(sender, settings));
  };
}

function chatMessage(sender, socket) {
  return (msg) => {
    replyBack(createMsg(msg, sender, filter));
    io.to(socket.id).emit("ack", { id: msg.id });
  };
}
function chat(sender, socket) {
  return (msg) => {
    let { sessionId, ...data } = msg;
    let text = filter.clean(data.text);
    console.log(sender.deviceId, text);
    sendMessageToUser(sessionId, {
      ...data,
      text,
      source: "user",
    });
    io.to(socket.id).emit("ack", { id: msg.id });
  };
}
function typing() {
  return (msg) => {
    let { sessionId, flag } = msg;
    io.to(sessionId).emit("typing", {
      typing: { flag },
      source: "user",
    });
  };
}

function sendMessageToUser(sessionId, msg) {
  io.to(sessionId).emit("serverMessage", msg);
}
module.exports.server = server;
module.exports.sendMessageToUser = sendMessageToUser;
module.exports.io = io;
module.exports.maxConnectedUsers = () => maxConnectedUsers;
