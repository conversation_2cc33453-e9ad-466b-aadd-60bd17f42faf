import React, { useEffect, useState, useRef } from 'react';
import { StyleSheet, View, ActivityIndicator } from 'react-native';

import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Home from './src/pages/Home';
import UserAgreement from './src/pages/UserAgreement';
import Camera from './src/Components/InputSection/Camera';
import Gallary from './src/Components/InputSection/gallary';
import TermsConditions from './src/pages/termsConditions';
import Settings from './src/pages/Settings';
import { Provider as PaperProvider } from 'react-native-paper';
import theme from './src/Constants/theme';
import { SettingsProvider } from './src/contexts/SettingsContext';
import { getAdFreeStatus } from './src/contexts/IAPContext';
import { RecoilRoot } from 'recoil';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { MenuProvider } from 'react-native-popup-menu';
import { MediaPage } from './src/Components/InputSection/Camera/MediaPage';
import SplashScreen from 'react-native-splash-screen';
import remoteConfig from '@react-native-firebase/remote-config';
import { initializeInterstitialAds } from './src/Components/ads/Interstitials';
import BackgroundSocketService from './src/services/backgroundSocketService';

import { initializeAppOpenAd, handleAppOpen, setupAppStateListener, cleanupAppStateListener } from './src/services/appOpenService';
import { KeyboardProvider } from "react-native-keyboard-controller";
import ErrorBoundary from './src/Components/ErrorBoundary';
const Stack = createNativeStackNavigator();

const App = () => {
  let [showTerms, setShowTerms] = useState();

  useEffect(() => {
    // Initialize app open ad service
    initializeAppOpenAd();

    // Setup app state listener for showing ads when app comes to foreground
    setupAppStateListener();

    // Handle app open logic when terms are accepted
    if (showTerms === false) {
      handleAppOpen(true);
    }

    return () => {
      // Cleanup app state listener
      cleanupAppStateListener();
    };
  }, [showTerms]);

  // Initialize background socket service for connection maintenance
  useEffect(() => {
    // The service automatically starts/stops based on app state
    console.log('Background socket service initialized');
  }, []);


  // Initialize ads after a delay to allow IAP context to load
  useEffect(() => {
    const initAds = () => {
      remoteConfig()
        .ensureInitialized()
        .finally(() => {
          // Use cached ad-free status
          const isAdFree = getAdFreeStatus();
          if (!isAdFree) {
            console.log('User does not have ad-free status, initializing ads');
            initializeInterstitialAds();
          } else {
            console.log('User has ad-free status, skipping ad initialization');
          }
        });
    };

    // Delay ad initialization to allow IAP context to initialize
    const timer = setTimeout(initAds, 2000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    console.log('Checking terms acceptance');

    AsyncStorage.getItem('termsAcceptance').then(data => {
      console.log(data);

      if (data === 'true') {
        setShowTerms(false);
      } else {
        setShowTerms(true);
      }
      console.log('hiding splash screen');

      SplashScreen.hide();
    });
  }, []);
  if (showTerms === undefined) {
    return (
      <View style={[styles.flex, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#007bff" />
      </View>
    );
  }

  return (
    <ErrorBoundary>
      <RecoilRoot>
        <SafeAreaProvider>
          <GestureHandlerRootView style={styles.flex}>
            <NavigationContainer>
              <SettingsProvider>
                <KeyboardProvider>
                  <MenuProvider>
                    <PaperProvider theme={theme}>
                      <Stack.Navigator screenOptions={{ headerShown: false }}>
                        {showTerms && (
                          <>
                            <Stack.Screen
                              name="UserAgreement"
                              component={UserAgreement}
                              navigationOptions={{
                                headerLeft: () => null,
                              }}
                            />
                            <Stack.Screen
                              name="TermsConditions"
                              component={TermsConditions}
                              navigationOptions={{
                                headerLeft: () => null,
                              }}
                            />
                          </>
                        )}
                        <Stack.Screen name="Home" component={Home} />

                        <Stack.Screen name="Settings" component={Settings} />
                        <Stack.Screen name="Camera" component={Camera} />
                        <Stack.Screen name="Gallary" component={Gallary} />
                        <Stack.Screen
                          name="MediaPage"
                          component={MediaPage}
                          options={{
                            animation: 'none',
                            presentation: 'transparentModal',
                          }}
                        />
                      </Stack.Navigator>
                    </PaperProvider>
                  </MenuProvider>
                </KeyboardProvider>
              </SettingsProvider>
            </NavigationContainer>
          </GestureHandlerRootView>
        </SafeAreaProvider>
      </RecoilRoot>
    </ErrorBoundary>
  );
};

const styles = StyleSheet.create({
  flex: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
});

export default App;
