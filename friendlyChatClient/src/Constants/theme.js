import { MD3LightTheme, configureFonts } from 'react-native-paper';
import { Platform } from 'react-native';

const baseFont = Platform.select({
  ios: 'Poppins-Regular',
  android: 'PoppinsRegular-B2Bw',
});

const baseFontBold = Platform.select({
  ios: 'Poppins-Bold',
  android: 'PoppinsBold-GdJA',
});

const fontConfig = {
  displayLarge: {
    ...MD3LightTheme.fonts.displayLarge,
    fontFamily: baseFontBold,
  },
  displayMedium: {
    ...MD3LightTheme.fonts.displayMedium,
    fontFamily: baseFontBold,
  },
  displaySmall: {
    ...MD3LightTheme.fonts.displaySmall,
    fontFamily: baseFontBold,
  },
  headlineLarge: {
    ...MD3LightTheme.fonts.headlineLarge,
    fontFamily: baseFontBold,
  },
  headlineMedium: {
    ...MD3LightTheme.fonts.headlineMedium,
    fontFamily: baseFontBold,
  },
  headlineSmall: {
    ...MD3LightTheme.fonts.headlineSmall,
    fontFamily: baseFontBold,
  },
  titleLarge: {
    ...MD3LightTheme.fonts.titleLarge,
    fontFamily: baseFontBold,
  },
  titleMedium: {
    ...MD3LightTheme.fonts.titleMedium,
    fontFamily: baseFontBold,
  },
  titleSmall: {
    ...MD3LightTheme.fonts.titleSmall,
    fontFamily: baseFontBold,
  },
  bodyLarge: {
    ...MD3LightTheme.fonts.bodyLarge,
    fontFamily: baseFont,
  },
  bodyMedium: {
    ...MD3LightTheme.fonts.bodyMedium,
    fontFamily: baseFont,
  },
  bodySmall: {
    ...MD3LightTheme.fonts.bodySmall,
    fontFamily: baseFont,
  },
  labelLarge: {
    ...MD3LightTheme.fonts.labelLarge,
    fontFamily: baseFont,
  },
  labelMedium: {
    ...MD3LightTheme.fonts.labelMedium,
    fontFamily: baseFont,
  },
  labelSmall: {
    ...MD3LightTheme.fonts.labelSmall,
    fontFamily: baseFont,
  },
};

export const theme = {
  ...MD3LightTheme,
  fonts: configureFonts({ config: fontConfig }),
  colors: {
    ...MD3LightTheme.colors,
    primary: '#2196f3',
    secondary: '#5276AC',
    tertiary: '#f7941d',
    background: '#FFFFFF',
    surface: '#E3E3E3',
    error: '#B00020',
    text: '#000000',
    disabled: '#949494',
    placeholder: '#B4B3B3',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#f50057',
    // Custom colors
    inputText: '#000000',
    strangerChatting: '#E3E3E3',
    userChatting: '#f7941d',
    icon: '#2F4D7F',
    textInputBackground: '#E3E3E3',
    actionBtn: '#5276AC',
  },
  roundness: 12,
  shadow: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
  },
  spacing: {
    xsmall: 4,
    small: 8,
    medium: 16,
    large: 24,
    xlarge: 32,
    xxlarge: 48,
  },
  typography: {
    heading: {
      fontSize: 28,
      fontWeight: '700',
      letterSpacing: 0.25,
    },
    subheading: {
      fontSize: 22,
      fontWeight: '600',
      letterSpacing: 0.15,
    },
    body: {
      fontSize: 16,
      fontWeight: '400',
      letterSpacing: 0.5,
    },
    caption: {
      fontSize: 14,
      fontWeight: '400',
      letterSpacing: 0.4,
      color: '#B0B0B0',
    },
    value: {
      fontSize: 42,
      fontWeight: '700',
      letterSpacing: -0.5,
    },
  },
  borderRadius: {
    small: 8,
    medium: 12,
    large: 16,
    pill: 50,
  },
};

export default theme;