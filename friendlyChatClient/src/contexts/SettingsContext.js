import AsyncStorage from '@react-native-async-storage/async-storage';
import React, {Component, createContext} from 'react';

const SettingsContext = createContext();

export default SettingsContext;

export class SettingsProvider extends Component {
  constructor(props) {
    console.log('settings provider');
    super(props);
    AsyncStorage.getItem('settings').then(data => {
      this.setState(JSON.parse(data));
    });
  }
  state = {
    name: undefined,
    gender: undefined,
    avatar: undefined,
  };
  setData = data => {
    this.setState(data);
  };
  render() {
    return (
      <SettingsContext.Provider
        value={{
          ...this.state,
          setSettings: this.setData,
        }}>
        {this.props.children}
      </SettingsContext.Provider>
    );
  }
}
