import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { AppState, ToastAndroid } from 'react-native';
import { getAvailablePurchases } from 'react-native-iap';
import { iapService } from '../services/iapService';
import { IAP_SKUS } from '../Constants/iapConstants';
import backendIAPService from '../services/backendIAPService';

const IAPContext = createContext();

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const SUBSCRIPTION_CHECK_INTERVAL = 30 * 60 * 1000; // 30 minutes
const EXPIRY_CHECK_INTERVAL = 60 * 1000; // 1 minute

export const IAPProvider = ({ children }) => {
    const [adFree, setAdFree] = useState(false);
    const [removeAdsProduct, setRemoveAdsProduct] = useState(null);
    const [adFreeOneMonthProduct, setAdFreeOneMonthProduct] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [lastChecked, setLastChecked] = useState(0);
    const [subscriptionExpiry, setSubscriptionExpiry] = useState(null);
    const [oneMonthExpiry, setOneMonthExpiry] = useState(null);

    const intervalRef = useRef(null);
    const expiryCheckRef = useRef(null);
    const appStateRef = useRef(AppState.currentState);

    // Optimized validation with caching
    const validateSubscription = useCallback(async (forceRefresh = false) => {
        const now = Date.now();

        // Return cached result if still valid and not forcing refresh
        if (!forceRefresh && (now - lastChecked) < CACHE_DURATION) {
            console.log('Using cached ad-free status:', adFree);
            return adFree;
        }

        try {
            setIsLoading(true);
            console.log('Validating subscription status...');

            // First try to get status from backend
            const backendStatus = await backendIAPService.getUserStatus();

            if (backendStatus.success) {
                console.log('Using backend IAP status:', backendStatus.status);
                setAdFree(backendStatus.status.hasAdFreeAccess);
                setSubscriptionExpiry(backendStatus.status.subscriptionExpiry ?
                    new Date(backendStatus.status.subscriptionExpiry) : null);
                setOneMonthExpiry(backendStatus.status.oneMonthExpiry ?
                    new Date(backendStatus.status.oneMonthExpiry) : null);
                setLastChecked(now);

                console.log('Backend validation complete:', {
                    adFree: backendStatus.status.hasAdFreeAccess,
                    subscriptionExpiry: backendStatus.status.subscriptionExpiry,
                    oneMonthExpiry: backendStatus.status.oneMonthExpiry
                });

                return backendStatus.status.hasAdFreeAccess;
            } else {
                console.log('Backend validation failed, falling back to local validation');
            }

            // Fallback to local validation
            const isAdFreeResult = await iapService.isAdFree();

            // Get detailed subscription info for expiry tracking
            const purchases = await getAvailablePurchases();

            // Track subscription expiry
            const subscription = purchases.find(p => p.productId === IAP_SKUS.REMOVE_ADS_Subscription);
            if (subscription) {
                const expiry = iapService.getSubscriptionExpiryDate(subscription);
                setSubscriptionExpiry(expiry);
            }

            // Track one-month purchase expiry
            const oneMonth = purchases.find(p => p.productId === IAP_SKUS.ONE_MONTH_AD_FREE);
            if (oneMonth) {
                const expiry = new Date(
                    JSON.parse(oneMonth.transactionReceipt).purchaseTime + 30 * 24 * 60 * 60 * 1000
                );
                setOneMonthExpiry(expiry);
            }

            setAdFree(isAdFreeResult);
            setLastChecked(now);

            console.log('Local validation complete:', {
                adFree: isAdFreeResult,
                subscriptionExpiry,
                oneMonthExpiry
            });

            return isAdFreeResult;
        } catch (error) {
            console.error('Error validating subscription:', error);
            // Don't update cache on error, keep previous state
            return adFree;
        } finally {
            setIsLoading(false);
        }
    }, [adFree, lastChecked, subscriptionExpiry, oneMonthExpiry]);

    // Check if subscription is about to expire (within 24 hours)
    const checkExpiryStatus = useCallback(() => {
        const now = new Date();
        const oneDayFromNow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

        let needsRefresh = false;

        // Check if subscription is expiring soon
        if (subscriptionExpiry && subscriptionExpiry <= oneDayFromNow) {
            console.log('Subscription expiring soon, refreshing status');
            needsRefresh = true;
        }

        // Check if one-month purchase is expiring soon
        if (oneMonthExpiry && oneMonthExpiry <= oneDayFromNow) {
            console.log('One-month purchase expiring soon, refreshing status');
            needsRefresh = true;
        }

        if (needsRefresh) {
            validateSubscription(true);
        }
    }, [subscriptionExpiry, oneMonthExpiry, validateSubscription]);

    // Handle app state changes
    const handleAppStateChange = useCallback((nextAppState) => {
        if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
            console.log('App became active, checking subscription status');
            // Force refresh when app becomes active
            validateSubscription(true);
        }
        appStateRef.current = nextAppState;
    }, [validateSubscription]);

    // Initialize IAP and set up intervals
    useEffect(() => {
        const initialize = async () => {
            try {
                // Initialize IAP service with timeout
                const initPromise = iapService.init();
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('IAP initialization timeout')), 10000)
                );

                await Promise.race([initPromise, timeoutPromise]);

                // Initial validation with fallback
                try {
                    await validateSubscription(true);
                } catch (validationError) {
                    console.warn('Initial validation failed, using default state:', validationError);
                    // Continue with default state (adFree = false)
                }

                // Load products in background - don't block UI
                Promise.all([
                    iapService.getRemoveAdsProduct().catch(e => {
                        console.warn('Failed to load remove ads product:', e);
                        return null;
                    }),
                    iapService.getAdFreeOneMonthProduct().catch(e => {
                        console.warn('Failed to load ad-free one month product:', e);
                        return null;
                    })
                ]).then(([removeAds, adFreeOneMonth]) => {
                    setRemoveAdsProduct(removeAds);
                    setAdFreeOneMonthProduct(adFreeOneMonth);
                });

            } catch (error) {
                console.error('Error initializing IAP:', error);
                // Set default state to allow app to continue
                setAdFree(false);
                setIsLoading(false);
            }
        };

        // Initialize in background to not block UI
        setTimeout(() => initialize(), 100);

        // Set up periodic validation
        intervalRef.current = setInterval(() => {
            validateSubscription(true).catch(error => {
                console.warn('Periodic validation failed:', error);
            });
        }, SUBSCRIPTION_CHECK_INTERVAL);

        // Set up expiry checking
        expiryCheckRef.current = setInterval(checkExpiryStatus, EXPIRY_CHECK_INTERVAL);

        // Listen for app state changes
        const subscription = AppState.addEventListener('change', handleAppStateChange);

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
            if (expiryCheckRef.current) {
                clearInterval(expiryCheckRef.current);
            }
            subscription?.remove();
            iapService.cleanup();
        };
    }, []);

    // Purchase handlers with cache invalidation and toast notifications
    const purchaseRemoveAds = async () => {
        setIsLoading(true);
        try {
            const success = await iapService.subscribeRemoveAds(
                removeAdsProduct,
                async (productId, purchaseStatus, backendStatus) => {
                    if (purchaseStatus) {
                        ToastAndroid.show('Thank you for purchasing Remove Ads!', ToastAndroid.SHORT);

                        if (backendStatus) {
                            // Use backend status if available
                            setAdFree(backendStatus.hasAdFreeAccess);
                            setSubscriptionExpiry(backendStatus.subscriptionExpiry ?
                                new Date(backendStatus.subscriptionExpiry) : null);
                            setOneMonthExpiry(backendStatus.oneMonthExpiry ?
                                new Date(backendStatus.oneMonthExpiry) : null);
                        } else {
                            // Fallback to immediate update
                            setAdFree(true);
                        }

                        setLastChecked(Date.now());
                        // Force refresh to get accurate expiry dates
                        setTimeout(() => validateSubscription(true), 1000);
                    }
                }
            );
            return success;
        } finally {
            setIsLoading(false);
        }
    };

    const purchaseAdFreeOneMonth = async () => {
        setIsLoading(true);
        try {
            const success = await iapService.purchaseAdFreeOneMonth(
                async (productId, purchaseStatus, backendStatus) => {
                    if (purchaseStatus) {
                        ToastAndroid.show('Thank you for purchasing Ad-Free for one month!', ToastAndroid.SHORT);

                        if (backendStatus) {
                            // Use backend status if available
                            setAdFree(backendStatus.hasAdFreeAccess);
                            setSubscriptionExpiry(backendStatus.subscriptionExpiry ?
                                new Date(backendStatus.subscriptionExpiry) : null);
                            setOneMonthExpiry(backendStatus.oneMonthExpiry ?
                                new Date(backendStatus.oneMonthExpiry) : null);
                        } else {
                            // Fallback to immediate update
                            setAdFree(true);
                        }

                        setLastChecked(Date.now());
                        // Force refresh to get accurate expiry dates
                        setTimeout(() => validateSubscription(true), 1000);
                    }
                }
            );
            return success;
        } finally {
            setIsLoading(false);
        }
    };

    // Fast, cached ad-free check
    const isAdFreeSync = useCallback(() => {
        console.log('Checking ad-free status synchronously...');

        const now = Date.now();

        // If cache is fresh, return immediately
        if ((now - lastChecked) < CACHE_DURATION) {
            return adFree;
        }
        console.log('Cache is stale, refreshing status...', isLoading, adFree);
        // If cache is stale, schedule async refresh for next tick to avoid setState during render
        // Only schedule if not already scheduled to prevent multiple calls
        if (!isLoading) {
            setTimeout(() => validateSubscription(false), 0);
        }
        return adFree;
    }, [adFree, lastChecked, validateSubscription, isLoading]);

    const contextValue = {
        // State
        adFree,
        adFreeForOneMonth: adFree, // Backward compatibility
        removeAdsProduct,
        adFreeOneMonthProduct,
        isLoading,
        subscriptionExpiry,
        oneMonthExpiry,

        // Methods
        purchaseRemoveAds,
        purchaseAdFreeOneMonth,
        validateSubscription,
        isAdFreeSync, // Fast synchronous check

        // Cache info (for debugging)
        lastChecked,
        cacheAge: Date.now() - lastChecked
    };

    // Update global status for non-React components
    React.useEffect(() => {
        _updateGlobalAdFreeStatus(adFree, isAdFreeSync);
    }, [adFree, isAdFreeSync]);

    return (
        <IAPContext.Provider value={contextValue}>
            {children}
        </IAPContext.Provider>
    );
};

export const useIAP = () => {
    const context = useContext(IAPContext);
    if (!context) {
        throw new Error('useIAP must be used within an IAPProvider');
    }
    return context;
};

// Fast hook for components that just need to check ad-free status
export const useIsAdFree = () => {
    const { isAdFreeSync } = useIAP();
    return isAdFreeSync();
};

// Global reference to the current ad-free status for non-React components
let globalAdFreeStatus = false;
let globalIsAdFreeSync = null;

// Function to get ad-free status from outside React components
export const getAdFreeStatus = () => {
    if (globalIsAdFreeSync) {
        return globalIsAdFreeSync();
    }
    return globalAdFreeStatus;
};

// Internal function to update global status (called from provider)
export const _updateGlobalAdFreeStatus = (adFree, isAdFreeSyncFn) => {
    globalAdFreeStatus = adFree;
    globalIsAdFreeSync = isAdFreeSyncFn;
};