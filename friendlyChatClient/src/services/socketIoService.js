import { io } from 'socket.io-client';

import DeviceInfo from 'react-native-device-info';
import { notifyUser } from './notificationService';
import MessageStatus from '../Constants/MessageStatus';
import InitMessage from '../Constants/MessageTextConst';
import Config from 'react-native-config';
let currentPartner;
let partnerId = -1;
let socket;

// Export socket instance for background service access
export const getSocket = () => socket;
export const isSocketConnected = () => socket?.connected === true;

const { backEndUrl, appName } = Config;
export let isRoomPageActive = true;
export const setIsRoomPage = flag => {
  isRoomPageActive = flag;
};
const SocketService = {
  init: (storedLinks, setLinks) => {
    socket = io(backEndUrl, {
      query: {
        deviceId: DeviceInfo.getUniqueIdSync(),
        channelId: appName
      },
    });
    socket.on('tabsLinks', ({ games, quiz }) => {
      let links = { ...storedLinks };
      if (games) {
        links.games = games;
      }
      if (quiz) {
        links.quiz = quiz;
      }
      setLinks(links);
    });
  },
  ack: updateMessage => {
    socket.on('ack', msg => {
      updateMessage(msg.id, 'status', MessageStatus.sent);
    });
  },
  serverMessage: (addServerMessage, setPartner) => {
    socket.on('serverMessage', msg => {
      //todo: add disconnect to wipe partner
      if (msg.partner) {
        setPartner(msg.partner);
        partnerId++;
        currentPartner = msg.partner;
        delete msg.partner;
      }
      if (msg.source === 'user') {
        msg.partner = partnerId;
      }
      if (msg.text) {
        notifyUser(msg.text, isRoomPageActive, currentPartner).catch(console.error);
        addServerMessage(msg);
      } else if (msg.attachment.type === 'image') {
        notifyUser('You have an Image', isRoomPageActive, currentPartner).catch(console.error);
        addServerMessage({
          uri: msg.attachment.payload.url,
          source: msg.source,
          partner: partnerId,
        });
      }
    });
  },
  userTyping: userTyping => {
    socket.on('typing', msg => {
      userTyping(msg.typing.flag);
    });
  },
  disconnect: (addServerMessage, setPartner, userTyping) => {
    socket.on('disconnect', reason => {
      // Clear partner data
      currentPartner = null;
      partnerId = -1;

      // Clear partner in UI
      if (setPartner) {
        setPartner(null);
      }

      // Stop typing indicator
      if (userTyping) {
        userTyping(false);
      }

      // Show appropriate message based on disconnect reason
      let message = InitMessage.disconnected;
      if (reason === 'io server disconnect') {
        message = InitMessage.serverDisconnected || 'Server disconnected you';
      } else if (reason === 'transport close') {
        message = InitMessage.connectionLost || 'Connection lost';
      }

      addServerMessage({
        text: message,
        source: 'sys',
        timestamp: Date.now()
      });
    });
  },

  // Method to check if socket is connected
  isConnected: () => socket?.connected === true,
  emit: (room, message, callback) => {
    if (socket) {
      socket.emit(room, message, callback);
    }
  },
  typing: flag => {
    if (currentPartner?.sessionId) {
      socket.emit('typing', { sessionId: currentPartner.sessionId, flag });
    }
  },
  chat: msg => {
    if (socket) {
      if (currentPartner?.sessionId) {
        socket.emit('chat', { ...msg, sessionId: currentPartner.sessionId });
      } else {
        socket.emit('chat message', msg);
      }
    }
  },
};
export default SocketService;
