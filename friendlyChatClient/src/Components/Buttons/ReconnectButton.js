import React from 'react';
import { StyleSheet, TouchableOpacity, Text, Platform } from 'react-native';
import { useRecoilValue } from 'recoil';
import Colors from '../../Constants/Colors';
import SocketService from '../../services/socketIoService';
import { sessionState } from '../../store/session/atoms';
import { showInterstitial } from '../ads/Interstitials';
import analytics from '@react-native-firebase/analytics';
import remoteConfig from '@react-native-firebase/remote-config';
import { useIsAdFree } from '../../contexts/IAPContext';

export default function ReconnectButton() {
  const session = useRecoilValue(sessionState);
  const isAdFree = useIsAdFree(); // Use optimized cached ad-free check

  const reconnect = () => {
    const AdInterval = remoteConfig().getValue('AdInterval').asNumber();
    analytics().logEvent('next_user');
    const partnersLenght = session.partners.length;

    // Use cached ad-free status - no async call needed!
    if (isAdFree) {
      // User has paid, skip ad
      console.log('User has ad-free status, skipping ad');
      SocketService.emit('reconnect');
    } else if (
      partnersLenght !== 0 &&
      Math.ceil(partnersLenght / 2) % AdInterval === 0
    ) {
      SocketService.emit('end');
      showInterstitial();
    } else {
      SocketService.emit('reconnect');
    }
  };
  return (
    <TouchableOpacity style={styles.btn} onPress={reconnect}>
      <Text style={styles.txt}>Find new friend</Text>
    </TouchableOpacity>
  );
}
const styles = StyleSheet.create({
  btn: {
    backgroundColor: Colors.actionBtnColor,
    alignSelf: 'center',
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    marginVertical: 10,
    borderRadius: 12,
  },
  txt: {
    color: 'white',
    textAlignVertical: 'center',
    fontFamily:
      Platform.OS === 'android' ? 'PoppinsRegular-B2Bw' : 'Poppins-Regular',
  },
});
